import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { SeoService } from '../../services/seo.service';
import { MetaApiService } from '../../services/meta-api.service';

@Component({
  selector: 'app-home',
  imports: [CommonModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit {

  featuredRecipes = [
    {
      id: 1,
      title: 'Creamy Mushroom Risotto',
      description: 'A rich and creamy Italian classic with wild mushrooms and parmesan cheese.',
      image: 'https://images.unsplash.com/photo-1476124369491-e7addf5db371?w=400&h=300&fit=crop',
      cookTime: '35 min',
      difficulty: 'Medium',
      rating: 4.8
    },
    {
      id: 2,
      title: 'Grilled Salmon with Herbs',
      description: 'Fresh Atlantic salmon grilled to perfection with aromatic herbs and lemon.',
      image: 'https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop',
      cookTime: '20 min',
      difficulty: 'Easy',
      rating: 4.9
    },
    {
      id: 3,
      title: 'Chocolate Lava Cake',
      description: 'Decadent chocolate dessert with a molten center that will melt your heart.',
      image: 'https://images.unsplash.com/photo-1563729784474-d77dbb933a9e?w=400&h=300&fit=crop',
      cookTime: '25 min',
      difficulty: 'Hard',
      rating: 4.7
    }
  ];

  cuisineTypes = [
    { name: 'Italian', icon: '🍝', count: '250+ recipes' },
    { name: 'Asian', icon: '🍜', count: '180+ recipes' },
    { name: 'Mexican', icon: '🌮', count: '120+ recipes' },
    { name: 'Mediterranean', icon: '🥗', count: '95+ recipes' },
    { name: 'Indian', icon: '🍛', count: '140+ recipes' },
    { name: 'French', icon: '🥐', count: '85+ recipes' }
  ];

  constructor(
    private seoService: SeoService,
    private metaApiService: MetaApiService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    this.loadMetaTags();
  }

  private loadMetaTags(): void {
    // For SSR, use default meta tags immediately
    const defaultMeta = this.seoService.getDefaultMetaTags('home');
    this.seoService.updateMetaTags(defaultMeta);

    // In browser, try to fetch dynamic meta tags
    if (isPlatformBrowser(this.platformId)) {
      this.metaApiService.getMetaTags('home').subscribe({
        next: (metaData) => {
          this.seoService.updateMetaTags(metaData);
        },
        error: (error) => {
          console.warn('Failed to load dynamic meta tags, using defaults:', error);
        }
      });
    }
  }

  getDifficultyClass(difficulty: string): string {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'badge bg-success';
      case 'medium': return 'badge bg-warning';
      case 'hard': return 'badge bg-danger';
      default: return 'badge bg-secondary';
    }
  }

  getStarRating(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('fas fa-star');
    }

    if (hasHalfStar) {
      stars.push('fas fa-star-half-alt');
    }

    while (stars.length < 5) {
      stars.push('far fa-star');
    }

    return stars;
  }
}
