import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SeoService } from '../../services/seo.service';
import { MetaApiService } from '../../services/meta-api.service';
import { RestaurantService } from '../../services/restaurant.service';
import { Restaurant } from '../../models/restaurant.model';

@Component({
  selector: 'app-home',
  imports: [CommonModule, FormsModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.css'
})
export class HomeComponent implements OnInit {

  searchQuery = '';
  restaurants: Restaurant[] = [];
  featuredRestaurants: Restaurant[] = [];
  filteredRestaurants: Restaurant[] = [];
  isSearching = false;
  showSearchResults = false;

  cuisineTypes = [
    { name: 'Italian', icon: '🍝', count: '25+ restaurants' },
    { name: 'Asian', icon: '🍜', count: '18+ restaurants' },
    { name: 'Mexican', icon: '🌮', count: '12+ restaurants' },
    { name: 'Mediterranean', icon: '🥗', count: '9+ restaurants' },
    { name: 'Indian', icon: '🍛', count: '14+ restaurants' },
    { name: 'American', icon: '🍔', count: '22+ restaurants' }
  ];

  popularCategories = [
    {
      name: 'Fast Food',
      icon: '🍔',
      image: 'https://images.unsplash.com/photo-**********-d89a9ad46330?w=300&h=200&fit=crop',
      count: '50+ restaurants'
    },
    {
      name: 'Pizza',
      icon: '🍕',
      image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
      count: '30+ restaurants'
    },
    {
      name: 'Healthy',
      icon: '🥗',
      image: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=300&h=200&fit=crop',
      count: '25+ restaurants'
    },
    {
      name: 'Desserts',
      icon: '🍰',
      image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=300&h=200&fit=crop',
      count: '15+ restaurants'
    }
  ];

  constructor(
    private seoService: SeoService,
    private metaApiService: MetaApiService,
    private restaurantService: RestaurantService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    this.loadMetaTags();
    this.loadFeaturedRestaurants();
  }

  private loadMetaTags(): void {
    // For SSR, use default meta tags immediately
    const defaultMeta = this.seoService.getDefaultMetaTags('home');
    this.seoService.updateMetaTags(defaultMeta);

    // In browser, try to fetch dynamic meta tags
    if (isPlatformBrowser(this.platformId)) {
      this.metaApiService.getMetaTags('home').subscribe({
        next: (metaData) => {
          this.seoService.updateMetaTags(metaData);
        },
        error: (error) => {
          console.warn('Failed to load dynamic meta tags, using defaults:', error);
        }
      });
    }
  }

  private loadFeaturedRestaurants(): void {
    this.restaurantService.getFeaturedRestaurants().subscribe({
      next: (restaurants) => {
        this.featuredRestaurants = restaurants.slice(0, 6); // Show top 6 featured restaurants
      },
      error: (error) => {
        console.error('Failed to load featured restaurants:', error);
      }
    });
  }

  onSearchInput(): void {
    if (this.searchQuery.trim().length === 0) {
      this.showSearchResults = false;
      this.filteredRestaurants = [];
      return;
    }

    if (this.searchQuery.trim().length < 2) {
      return;
    }

    this.isSearching = true;
    this.restaurantService.searchRestaurants(this.searchQuery.trim()).subscribe({
      next: (restaurants) => {
        this.filteredRestaurants = restaurants;
        this.showSearchResults = true;
        this.isSearching = false;
      },
      error: (error) => {
        console.error('Search failed:', error);
        this.isSearching = false;
      }
    });
  }

  selectRestaurant(restaurant: Restaurant): void {
    this.restaurantService.setSelectedRestaurant(restaurant);
    this.router.navigate(['/restaurant', restaurant.slug]);
  }

  searchByCuisine(cuisine: string): void {
    this.restaurantService.getRestaurants({ cuisine: [cuisine] }).subscribe({
      next: (restaurants) => {
        this.filteredRestaurants = restaurants;
        this.showSearchResults = true;
        this.searchQuery = cuisine;
      },
      error: (error) => {
        console.error('Cuisine search failed:', error);
      }
    });
  }

  getPriceRangeDisplay(priceRange: string): string {
    switch (priceRange) {
      case 'budget': return '$';
      case 'mid-range': return '$$';
      case 'fine-dining': return '$$$';
      default: return '$$';
    }
  }

  getStarRating(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push('fas fa-star');
    }

    if (hasHalfStar) {
      stars.push('fas fa-star-half-alt');
    }

    while (stars.length < 5) {
      stars.push('far fa-star');
    }

    return stars;
  }

  clearSearch(): void {
    this.searchQuery = '';
    this.showSearchResults = false;
    this.filteredRestaurants = [];
  }
}
