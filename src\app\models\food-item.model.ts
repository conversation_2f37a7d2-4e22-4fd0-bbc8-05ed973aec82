export interface FoodItem {
  id: string;
  restaurantId: string;
  name: string;
  slug: string; // URL-friendly name for SEO
  description: string;
  shortDescription: string;
  price: number;
  originalPrice?: number; // For discounted items
  category: string;
  subcategory?: string;
  images: string[];
  isVegetarian: boolean;
  isVegan: boolean;
  isGlutenFree: boolean;
  isSpicy: boolean;
  spiceLevel?: 1 | 2 | 3 | 4 | 5;
  allergens: string[];
  nutritionalInfo?: NutritionalInfo;
  ingredients: string[];
  preparationTime: number; // in minutes
  isAvailable: boolean;
  isPopular: boolean;
  isFeatured: boolean;
  rating: number;
  reviewCount: number;
  tags: string[];
  customizations?: Customization[];
  addOns?: AddOn[];
  createdAt: Date;
  updatedAt: Date;
}

export interface NutritionalInfo {
  calories: number;
  protein: number; // in grams
  carbohydrates: number; // in grams
  fat: number; // in grams
  fiber: number; // in grams
  sugar: number; // in grams
  sodium: number; // in milligrams
  servingSize: string;
}

export interface Customization {
  id: string;
  name: string;
  type: 'single' | 'multiple';
  required: boolean;
  options: CustomizationOption[];
}

export interface CustomizationOption {
  id: string;
  name: string;
  price: number; // additional price
  isDefault?: boolean;
}

export interface AddOn {
  id: string;
  name: string;
  description: string;
  price: number;
  image?: string;
  isAvailable: boolean;
}

export interface FoodCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  image: string;
  icon: string;
  sortOrder: number;
  isActive: boolean;
}

export interface FoodItemSearchFilters {
  query?: string;
  category?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  dietary?: {
    vegetarian?: boolean;
    vegan?: boolean;
    glutenFree?: boolean;
  };
  spiceLevel?: number[];
  rating?: number;
  isPopular?: boolean;
  isAvailable?: boolean;
}

export interface CartItem {
  foodItem: FoodItem;
  quantity: number;
  customizations?: { [customizationId: string]: string[] };
  addOns?: string[];
  specialInstructions?: string;
  totalPrice: number;
}
