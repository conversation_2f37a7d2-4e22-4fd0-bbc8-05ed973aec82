import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { SeoService } from '../../services/seo.service';
import { MetaApiService } from '../../services/meta-api.service';

@Component({
  selector: 'app-about',
  imports: [CommonModule],
  templateUrl: './about.component.html',
  styleUrl: './about.component.css'
})
export class AboutComponent implements OnInit {

  teamMembers = [
    {
      name: '<PERSON>',
      role: 'Head Chef & Founder',
      image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
      bio: 'With 15 years of culinary experience, <PERSON> founded FoodieHub to share her passion for creating accessible, delicious recipes.',
      specialties: ['Italian Cuisine', 'Pastry Arts', 'Farm-to-Table']
    },
    {
      name: '<PERSON>',
      role: 'Executive Chef',
      image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
      bio: '<PERSON> brings expertise in Asian fusion cuisine and innovative cooking techniques to our recipe development team.',
      specialties: ['Asian Fusion', 'Molecular Gastronomy', 'Seafood']
    },
    {
      name: 'Elena Rodriguez',
      role: 'Nutrition Specialist',
      image: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=300&fit=crop&crop=face',
      bio: 'Elena ensures all our recipes are not only delicious but also nutritionally balanced and health-conscious.',
      specialties: ['Healthy Cooking', 'Dietary Restrictions', 'Meal Planning']
    }
  ];

  stats = [
    { number: '10,000+', label: 'Recipes', icon: 'fas fa-utensils' },
    { number: '50,000+', label: 'Community Members', icon: 'fas fa-users' },
    { number: '25+', label: 'Countries Represented', icon: 'fas fa-globe' },
    { number: '4.9/5', label: 'Average Rating', icon: 'fas fa-star' }
  ];

  constructor(
    private seoService: SeoService,
    private metaApiService: MetaApiService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    this.loadMetaTags();
  }

  private loadMetaTags(): void {
    // For SSR, use default meta tags immediately
    const defaultMeta = this.seoService.getDefaultMetaTags('about');
    this.seoService.updateMetaTags(defaultMeta);

    // In browser, try to fetch dynamic meta tags
    if (isPlatformBrowser(this.platformId)) {
      this.metaApiService.getMetaTags('about').subscribe({
        next: (metaData) => {
          this.seoService.updateMetaTags(metaData);
        },
        error: (error) => {
          console.warn('Failed to load dynamic meta tags, using defaults:', error);
        }
      });
    }
  }
}
