.hero-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.search-container .form-control {
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-container .btn {
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.quick-help-card {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border: 1px solid #f8f9fa;
}

.quick-help-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
  border-color: var(--primary-color);
}

.help-icon {
  transition: transform 0.3s ease;
}

.quick-help-card:hover .help-icon {
  transform: scale(1.1);
}

.faq-category {
  margin-bottom: 3rem;
}

.category-header h4 {
  color: var(--text-dark);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 0.5rem;
}

.accordion-item {
  border: 1px solid #e9ecef;
  border-radius: 10px !important;
  overflow: hidden;
}

.accordion-button {
  background-color: #f8f9fa;
  border: none;
  font-weight: 600;
  color: var(--text-dark);
  padding: 1.25rem 1.5rem;
}

.accordion-button:not(.collapsed) {
  background-color: var(--primary-color);
  color: white;
  box-shadow: none;
}

.accordion-button:focus {
  box-shadow: none;
  border: none;
}

.accordion-button::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-button:not(.collapsed)::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.accordion-body {
  padding: 1.5rem;
  background-color: white;
  color: var(--text-light);
  line-height: 1.6;
}

.contact-option {
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border: 1px solid #f8f9fa;
}

.contact-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
  border-color: var(--primary-color);
}

@media (max-width: 768px) {
  .hero-section {
    text-align: center;
  }

  .search-container {
    margin-top: 2rem;
  }

  .quick-help-card {
    margin-bottom: 1.5rem;
  }

  .contact-option {
    margin-bottom: 1rem;
  }

  .accordion-button {
    font-size: 0.9rem;
    padding: 1rem;
  }

  .accordion-body {
    padding: 1rem;
  }
}