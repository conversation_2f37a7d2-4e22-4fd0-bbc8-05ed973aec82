import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { Meta, Title } from '@angular/platform-browser';
import { isPlatformBrowser } from '@angular/common';
import { RestaurantSEO } from '../models/restaurant.model';

export interface MetaTagData {
  title: string;
  description: string;
  keywords: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterCard?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SeoService {

  constructor(
    private meta: Meta,
    private title: Title,
    @Inject(PLATFORM_ID) private platformId: Object
  ) { }

  updateMetaTags(data: MetaTagData | RestaurantSEO): void {
    // Update title
    this.title.setTitle(data.title);

    // Update basic meta tags
    this.meta.updateTag({ name: 'description', content: data.description });
    this.meta.updateTag({ name: 'keywords', content: data.keywords });

    // Update Open Graph tags
    this.meta.updateTag({ property: 'og:title', content: data.ogTitle || data.title });
    this.meta.updateTag({ property: 'og:description', content: data.ogDescription || data.description });
    this.meta.updateTag({ property: 'og:type', content: 'website' });
    
    if (data.ogImage) {
      this.meta.updateTag({ property: 'og:image', content: data.ogImage });
    }
    
    if (data.ogUrl) {
      this.meta.updateTag({ property: 'og:url', content: data.ogUrl });
    }

    // Update Twitter Card tags
    this.meta.updateTag({ name: 'twitter:card', content: data.twitterCard || 'summary_large_image' });
    this.meta.updateTag({ name: 'twitter:title', content: data.twitterTitle || data.title });
    this.meta.updateTag({ name: 'twitter:description', content: data.twitterDescription || data.description });
    
    if (data.twitterImage) {
      this.meta.updateTag({ name: 'twitter:image', content: data.twitterImage });
    }

    // Update canonical URL
    if (isPlatformBrowser(this.platformId)) {
      this.updateCanonicalUrl(data.ogUrl || window.location.href);
    }
  }

  private updateCanonicalUrl(url: string): void {
    let link: HTMLLinkElement | null = document.querySelector('link[rel="canonical"]');
    
    if (!link) {
      link = document.createElement('link');
      link.setAttribute('rel', 'canonical');
      document.head.appendChild(link);
    }
    
    link.setAttribute('href', url);
  }

  // Method to get default meta tags for pre-rendering
  getDefaultMetaTags(page: string): MetaTagData {
    const baseUrl = 'https://yourfoodwebsite.com'; // Replace with your actual domain
    
    const defaultTags: { [key: string]: MetaTagData } = {
      home: {
        title: 'FoodieHub - Discover Amazing Recipes & Culinary Adventures',
        description: 'Explore delicious recipes, cooking tips, and culinary adventures. Join our food community and discover your next favorite dish.',
        keywords: 'recipes, cooking, food, culinary, kitchen, ingredients, meals, cooking tips',
        ogTitle: 'FoodieHub - Your Ultimate Food Destination',
        ogDescription: 'Discover amazing recipes and join our culinary community',
        ogImage: `${baseUrl}/assets/images/foodie-hub-og.jpg`,
        ogUrl: `${baseUrl}/`,
        twitterCard: 'summary_large_image'
      },
      about: {
        title: 'About FoodieHub - Our Culinary Journey',
        description: 'Learn about FoodieHub\'s mission to bring food lovers together and share amazing culinary experiences from around the world.',
        keywords: 'about foodie hub, culinary journey, food community, cooking passion',
        ogTitle: 'About FoodieHub - Our Story',
        ogDescription: 'Discover the story behind FoodieHub and our passion for food',
        ogImage: `${baseUrl}/assets/images/about-og.jpg`,
        ogUrl: `${baseUrl}/about`,
        twitterCard: 'summary_large_image'
      },
      help: {
        title: 'Help & FAQ - FoodieHub Support',
        description: 'Find answers to frequently asked questions about FoodieHub. Get help with recipes, cooking tips, and using our platform.',
        keywords: 'help, FAQ, support, cooking help, recipe questions, foodie hub support',
        ogTitle: 'FoodieHub Help Center',
        ogDescription: 'Get help and find answers to your cooking and recipe questions',
        ogImage: `${baseUrl}/assets/images/help-og.jpg`,
        ogUrl: `${baseUrl}/help`,
        twitterCard: 'summary_large_image'
      }
    };

    return defaultTags[page] || defaultTags['home'];
  }
}
