/* You can add global styles to this file, and also import other style files */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Food-themed global styles */
:root {
  --primary-color: #ff6b35;
  --secondary-color: #f7931e;
  --accent-color: #2e8b57;
  --text-dark: #2c3e50;
  --text-light: #7f8c8d;
  --bg-light: #f8f9fa;
  --bg-cream: #fef9e7;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--text-dark);
  line-height: 1.6;
}

.hero-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 100px 0;
}

.food-card {
  border: none;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  transition: transform 0.3s ease;
}

.food-card:hover {
  transform: translateY(-5px);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: 25px;
  padding: 12px 30px;
  font-weight: 600;
}

.btn-primary:hover {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.section-title {
  color: var(--text-dark);
  font-weight: 700;
  margin-bottom: 3rem;
}

.text-primary-custom {
  color: var(--primary-color) !important;
}

.bg-cream {
  background-color: var(--bg-cream);
}
