import { Routes } from '@angular/router';
import { FullComponent } from './layout/full/full.component';
import { HomeComponent } from './pages/home/<USER>';
import { AboutComponent } from './pages/about/about.component';
import { HelpSectionComponent } from './pages/help-section/help-section.component';
import { RestaurantComponent } from './pages/restaurant/restaurant.component';
import { FoodItemComponent } from './pages/food-item/food-item.component';

export const routes: Routes = [
    {
        path: '',
        component: FullComponent,
        children: [
            {
                path: '',
                component: HomeComponent,
                title: 'FoodieHub - Discover Amazing Restaurants & Order Online'
            },
            {
                path: 'restaurant/:slug',
                component: RestaurantComponent,
                title: 'Restaurant Menu - FoodieHub'
            },
            {
                path: 'restaurant/:restaurantSlug/item/:itemSlug',
                component: FoodItemComponent,
                title: 'Food Item - FoodieHub'
            },
            {
                path: 'about',
                component: AboutComponent,
                title: 'About FoodieHub - Our Culinary Journey'
            },
            {
                path: 'help',
                component: HelpSectionComponent,
                title: 'Help & FAQ - FoodieHub Support'
            }
        ]
    },
    {
        path: '**',
        redirectTo: ''
    }
];
