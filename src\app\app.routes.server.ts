import { RenderMode, ServerRoute } from '@angular/ssr';

export const serverRoutes: ServerRoute[] = [
  // Static pages - prerender for SEO
  {
    path: '',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'about',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'help',
    renderMode: RenderMode.Prerender
  },
  // Restaurant pages - SSR for dynamic content
  {
    path: 'restaurant/**',
    renderMode: RenderMode.Server
  },
  // Fallback for other routes
  {
    path: '**',
    renderMode: RenderMode.Server
  }
];
