import { RenderMode, ServerRoute } from '@angular/ssr';

export const serverRoutes: ServerRoute[] = [
  // Static pages - prerender for SEO
  {
    path: '',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'about',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'help',
    renderMode: RenderMode.Prerender
  },
  // Restaurant pages - Prerender for SEO optimization
  {
    path: 'restaurant/bella-italia',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'restaurant/spice-garden',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'restaurant/burger-palace',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'restaurant/sushi-zen',
    renderMode: RenderMode.Prerender
  },
  {
    path: 'restaurant/**',
    renderMode: RenderMode.Server
  },
  // Fallback for other routes
  {
    path: '**',
    renderMode: RenderMode.Server
  }
];
