import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { SeoService } from '../../services/seo.service';
import { MetaApiService } from '../../services/meta-api.service';

@Component({
  selector: 'app-help-section',
  imports: [CommonModule],
  templateUrl: './help-section.component.html',
  styleUrl: './help-section.component.css'
})
export class HelpSectionComponent implements OnInit {

  faqCategories = [
    {
      title: 'Getting Started',
      icon: 'fas fa-play-circle',
      faqs: [
        {
          question: 'How do I create an account?',
          answer: 'Click the "Sign Up" button in the top right corner and fill out the registration form. You\'ll receive a confirmation email to activate your account.',
          isOpen: false
        },
        {
          question: 'Is FoodieHub free to use?',
          answer: 'Yes! FoodieHub is completely free. You can browse recipes, save favorites, and join our community without any cost.',
          isOpen: false
        },
        {
          question: 'How do I search for recipes?',
          answer: 'Use the search bar at the top of the page. You can search by ingredient, cuisine type, cooking time, or recipe name.',
          isOpen: false
        }
      ]
    },
    {
      title: 'Recipes & Cooking',
      icon: 'fas fa-utensils',
      faqs: [
        {
          question: 'Can I substitute ingredients in recipes?',
          answer: 'Many ingredients can be substituted! Check our ingredient substitution guide or ask our community for suggestions. Common substitutions are usually listed in recipe notes.',
          isOpen: false
        },
        {
          question: 'What if I don\'t have a specific cooking tool?',
          answer: 'Most recipes include alternative methods. If you\'re missing a tool, check the recipe notes or comments for suggestions from other users.',
          isOpen: false
        },
        {
          question: 'How accurate are the cooking times?',
          answer: 'Our cooking times are tested averages. Actual times may vary based on your equipment, altitude, and ingredient variations. Always check for doneness indicators.',
          isOpen: false
        },
        {
          question: 'Can I scale recipes up or down?',
          answer: 'Yes! Most recipes can be scaled. Use our recipe calculator feature, but note that cooking times may need adjustment for larger or smaller portions.',
          isOpen: false
        }
      ]
    },
    {
      title: 'Account & Profile',
      icon: 'fas fa-user-circle',
      faqs: [
        {
          question: 'How do I save my favorite recipes?',
          answer: 'Click the heart icon on any recipe to save it to your favorites. You can access saved recipes from your profile page.',
          isOpen: false
        },
        {
          question: 'Can I share my own recipes?',
          answer: 'Absolutely! Use the "Submit Recipe" feature to share your creations with our community. All submissions are reviewed before publishing.',
          isOpen: false
        },
        {
          question: 'How do I update my profile information?',
          answer: 'Go to your profile page and click "Edit Profile". You can update your name, bio, dietary preferences, and profile picture.',
          isOpen: false
        }
      ]
    },
    {
      title: 'Technical Support',
      icon: 'fas fa-cog',
      faqs: [
        {
          question: 'The website is loading slowly. What can I do?',
          answer: 'Try clearing your browser cache, checking your internet connection, or using a different browser. If issues persist, contact our support team.',
          isOpen: false
        },
        {
          question: 'I\'m not receiving email notifications.',
          answer: 'Check your spam folder and ensure foodiehub.com is in your safe senders list. You can also update your email preferences in your account settings.',
          isOpen: false
        },
        {
          question: 'How do I report a problem with a recipe?',
          answer: 'Use the "Report Issue" button on the recipe page or contact us directly. Please provide details about the problem you encountered.',
          isOpen: false
        }
      ]
    }
  ];

  quickHelp = [
    {
      title: 'Recipe Calculator',
      description: 'Automatically scale recipe ingredients for any serving size',
      icon: 'fas fa-calculator',
      link: '#calculator'
    },
    {
      title: 'Cooking Tips',
      description: 'Essential techniques and tips from professional chefs',
      icon: 'fas fa-lightbulb',
      link: '#tips'
    },
    {
      title: 'Video Tutorials',
      description: 'Step-by-step cooking videos for complex techniques',
      icon: 'fas fa-video',
      link: '#videos'
    },
    {
      title: 'Community Forum',
      description: 'Ask questions and get help from fellow food enthusiasts',
      icon: 'fas fa-comments',
      link: '#forum'
    }
  ];

  constructor(
    private seoService: SeoService,
    private metaApiService: MetaApiService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    this.loadMetaTags();
  }

  private loadMetaTags(): void {
    // For SSR, use default meta tags immediately
    const defaultMeta = this.seoService.getDefaultMetaTags('help');
    this.seoService.updateMetaTags(defaultMeta);

    // In browser, try to fetch dynamic meta tags
    if (isPlatformBrowser(this.platformId)) {
      this.metaApiService.getMetaTags('help').subscribe({
        next: (metaData) => {
          this.seoService.updateMetaTags(metaData);
        },
        error: (error) => {
          console.warn('Failed to load dynamic meta tags, using defaults:', error);
        }
      });
    }
  }

  toggleFaq(categoryIndex: number, faqIndex: number): void {
    this.faqCategories[categoryIndex].faqs[faqIndex].isOpen =
      !this.faqCategories[categoryIndex].faqs[faqIndex].isOpen;
  }
}
