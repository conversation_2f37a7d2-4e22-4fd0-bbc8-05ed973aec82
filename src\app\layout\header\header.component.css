.navbar-brand {
  font-size: 1.8rem !important;
}

.nav-link {
  transition: color 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--primary-color) !important;
}

.nav-link.active {
  color: var(--primary-color) !important;
  font-weight: 600;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.navbar-toggler {
  border: none;
  padding: 4px 8px;
}

.navbar-toggler:focus {
  box-shadow: none;
}

@media (max-width: 991.98px) {
  .navbar-nav {
    text-align: center;
    padding-top: 1rem;
  }

  .nav-link.active::after {
    display: none;
  }
}