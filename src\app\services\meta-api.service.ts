import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, catchError } from 'rxjs';
import { MetaTagData } from './seo.service';

@Injectable({
  providedIn: 'root'
})
export class MetaApiService {
  private apiUrl = 'https://api.yourfoodwebsite.com/meta'; // Replace with your actual API endpoint

  constructor(private http: HttpClient) { }

  getMetaTags(page: string): Observable<MetaTagData> {
    // In a real application, this would make an HTTP call to your API
    // For demo purposes, we'll simulate API responses with fallback to defaults
    
    return this.http.get<MetaTagData>(`${this.apiUrl}/${page}`).pipe(
      catchError(() => {
        // Fallback to mock data if API is not available
        return of(this.getMockMetaTags(page));
      })
    );
  }

  private getMockMetaTags(page: string): MetaTagData {
    const baseUrl = 'https://yourfoodwebsite.com';
    
    const mockData: { [key: string]: MetaTagData } = {
      home: {
        title: 'FoodieHub - Discover Amazing Recipes & Culinary Adventures',
        description: 'Explore delicious recipes, cooking tips, and culinary adventures. Join our food community and discover your next favorite dish with over 10,000 recipes.',
        keywords: 'recipes, cooking, food, culinary, kitchen, ingredients, meals, cooking tips, food blog',
        ogTitle: 'FoodieHub - Your Ultimate Food Destination',
        ogDescription: 'Discover amazing recipes and join our culinary community with thousands of food lovers',
        ogImage: `${baseUrl}/assets/images/foodie-hub-home.jpg`,
        ogUrl: `${baseUrl}/`,
        twitterCard: 'summary_large_image',
        twitterTitle: 'FoodieHub - Amazing Recipes Await',
        twitterDescription: 'Join thousands of food lovers discovering amazing recipes daily',
        twitterImage: `${baseUrl}/assets/images/foodie-hub-twitter.jpg`
      },
      about: {
        title: 'About FoodieHub - Our Culinary Journey & Mission',
        description: 'Learn about FoodieHub\'s mission to bring food lovers together. Founded by passionate chefs, we share amazing culinary experiences from around the world.',
        keywords: 'about foodie hub, culinary journey, food community, cooking passion, chef stories',
        ogTitle: 'About FoodieHub - Our Culinary Story',
        ogDescription: 'Discover the passionate team behind FoodieHub and our mission to connect food lovers worldwide',
        ogImage: `${baseUrl}/assets/images/about-team.jpg`,
        ogUrl: `${baseUrl}/about`,
        twitterCard: 'summary_large_image',
        twitterTitle: 'Meet the FoodieHub Team',
        twitterDescription: 'Passionate chefs and food lovers bringing you the best culinary experiences',
        twitterImage: `${baseUrl}/assets/images/about-twitter.jpg`
      },
      help: {
        title: 'Help & FAQ - FoodieHub Support Center',
        description: 'Find answers to frequently asked questions about FoodieHub. Get help with recipes, cooking techniques, ingredient substitutions, and using our platform.',
        keywords: 'help, FAQ, support, cooking help, recipe questions, ingredient substitutions, cooking techniques',
        ogTitle: 'FoodieHub Help Center - Cooking Support',
        ogDescription: 'Get expert help with cooking questions, recipe modifications, and platform support',
        ogImage: `${baseUrl}/assets/images/help-support.jpg`,
        ogUrl: `${baseUrl}/help`,
        twitterCard: 'summary_large_image',
        twitterTitle: 'FoodieHub Help - Cooking Support',
        twitterDescription: 'Expert cooking help and recipe support for all your culinary questions',
        twitterImage: `${baseUrl}/assets/images/help-twitter.jpg`
      }
    };

    return mockData[page] || mockData['home'];
  }

  // Method to update meta tags via API (for admin purposes)
  updateMetaTags(page: string, metaData: MetaTagData): Observable<any> {
    return this.http.put(`${this.apiUrl}/${page}`, metaData).pipe(
      catchError(error => {
        console.error('Failed to update meta tags:', error);
        return of({ success: false, error: error.message });
      })
    );
  }
}
