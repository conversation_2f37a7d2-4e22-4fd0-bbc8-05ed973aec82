<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>FoodieHub - Discover Amazing Recipes & Culinary Adventures</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Explore delicious recipes, cooking tips, and culinary adventures. Join our food community and discover your next favorite dish.">
  <meta name="keywords" content="recipes, cooking, food, culinary, kitchen, ingredients, meals, cooking tips">
  <meta name="author" content="FoodieHub">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="FoodieHub - Your Ultimate Food Destination">
  <meta property="og:description" content="Discover amazing recipes and join our culinary community">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://yourfoodwebsite.com/">
  <meta property="og:image" content="https://yourfoodwebsite.com/assets/images/foodie-hub-og.jpg">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="FoodieHub - Amazing Recipes Await">
  <meta name="twitter:description" content="Join thousands of food lovers discovering amazing recipes daily">
  <meta name="twitter:image" content="https://yourfoodwebsite.com/assets/images/foodie-hub-twitter.jpg">

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Preconnect for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <app-root></app-root>

  <!-- Bootstrap JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
