<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>FoodieHub - Discover Amazing Restaurants & Order Online</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="Discover and order from the best restaurants in your area. Fast delivery, great food, amazing experience with over 500+ restaurants.">
  <meta name="keywords" content="food delivery, restaurants, order online, cuisine, dining, fast delivery, food ordering app">
  <meta name="author" content="FoodieHub">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="FoodieHub - Your Ultimate Food Delivery Platform">
  <meta property="og:description" content="Order from the best restaurants with fast delivery. Discover amazing food from 500+ restaurants.">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://yourfoodplatform.com/">
  <meta property="og:image" content="https://yourfoodplatform.com/assets/images/foodie-hub-og.jpg">

  <!-- Twitter Card Meta Tags -->
  <meta name="twitter:card" content="summary_large_image">
  <meta name="twitter:title" content="FoodieHub - Order Food Online">
  <meta name="twitter:description" content="Fast food delivery from 500+ restaurants. Order now!">
  <meta name="twitter:image" content="https://yourfoodplatform.com/assets/images/foodie-hub-twitter.jpg">

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- Preconnect for performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <app-root></app-root>

  <!-- Bootstrap JavaScript -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
