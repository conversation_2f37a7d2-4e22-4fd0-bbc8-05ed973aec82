import { Component, OnInit, OnD<PERSON>roy, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { RestaurantService } from '../../services/restaurant.service';
import { FoodItemService } from '../../services/food-item.service';
import { SeoService } from '../../services/seo.service';
import { Restaurant } from '../../models/restaurant.model';
import { FoodItem, FoodCategory } from '../../models/food-item.model';

@Component({
  selector: 'app-restaurant',
  imports: [CommonModule],
  templateUrl: './restaurant.component.html',
  styleUrl: './restaurant.component.css'
})
export class RestaurantComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  restaurant: Restaurant | null = null;
  foodItems: FoodItem[] = [];
  categories: FoodCategory[] = [];
  selectedCategory: string = 'all';
  filteredFoodItems: FoodItem[] = [];
  isLoading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private restaurantService: RestaurantService,
    private foodItemService: FoodItemService,
    private seoService: SeoService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      const restaurantSlug = params['slug'];
      if (restaurantSlug) {
        this.loadRestaurant(restaurantSlug);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadRestaurant(slug: string): void {
    this.isLoading = true;
    this.error = null;

    this.restaurantService.getRestaurantBySlug(slug).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (restaurant) => {
        if (restaurant) {
          this.restaurant = restaurant;
          this.restaurantService.setSelectedRestaurant(restaurant);
          this.loadRestaurantSEO(slug);
          this.loadFoodItems(restaurant.id);
          this.loadCategories(restaurant.id);
        } else {
          this.error = 'Restaurant not found';
          this.isLoading = false;
        }
      },
      error: (error) => {
        console.error('Error loading restaurant:', error);
        this.error = 'Failed to load restaurant';
        this.isLoading = false;
      }
    });
  }

  private loadRestaurantSEO(slug: string): void {
    this.restaurantService.getRestaurantSEO(slug).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (seoData) => {
        this.seoService.updateMetaTags(seoData);
      },
      error: (error) => {
        console.warn('Failed to load restaurant SEO data:', error);
      }
    });
  }

  private loadFoodItems(restaurantId: string): void {
    this.foodItemService.getFoodItemsByRestaurant(restaurantId).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (items) => {
        this.foodItems = items;
        this.filteredFoodItems = items;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading food items:', error);
        this.isLoading = false;
      }
    });
  }

  private loadCategories(restaurantId: string): void {
    this.foodItemService.getFoodCategories(restaurantId).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (categories) => {
        this.categories = categories;
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      }
    });
  }

  selectCategory(categorySlug: string): void {
    this.selectedCategory = categorySlug;
    
    if (categorySlug === 'all') {
      this.filteredFoodItems = this.foodItems;
    } else {
      const category = this.categories.find(c => c.slug === categorySlug);
      if (category) {
        this.filteredFoodItems = this.foodItems.filter(item => 
          item.category === category.name
        );
      }
    }
  }

  getStarRating(rating: number): string[] {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < fullStars; i++) {
      stars.push('fas fa-star');
    }
    
    if (hasHalfStar) {
      stars.push('fas fa-star-half-alt');
    }
    
    while (stars.length < 5) {
      stars.push('far fa-star');
    }
    
    return stars;
  }

  getPriceRangeDisplay(priceRange: string): string {
    switch (priceRange) {
      case 'budget': return '$';
      case 'mid-range': return '$$';
      case 'fine-dining': return '$$$';
      default: return '$$';
    }
  }

  formatOpeningHours(openingHours: any): string {
    const today = new Date().getDay(); // 0 = Sunday, 1 = Monday, etc.
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const todayName = dayNames[today];

    if (todayName && openingHours[todayName]) {
      const schedule = openingHours[todayName];
      if (schedule.isOpen && schedule.openTime && schedule.closeTime) {
        return `${schedule.openTime} - ${schedule.closeTime}`;
      }
    }
    return 'Closed';
  }

  addToCart(foodItem: FoodItem): void {
    // TODO: Implement cart functionality
    console.log('Adding to cart:', foodItem);
  }

  viewFoodItem(foodItem: FoodItem): void {
    if (this.restaurant) {
      this.router.navigate(['/restaurant', this.restaurant.slug, 'item', foodItem.slug]);
    }
  }

  goBack(): void {
    this.router.navigate(['/']);
  }

  getCategoryItemCount(categoryName: string): number {
    return this.foodItems.filter(item => item.category === categoryName).length;
  }

  getSelectedCategoryName(): string {
    if (this.selectedCategory === 'all') {
      return 'All Menu Items';
    }
    const category = this.categories.find(c => c.slug === this.selectedCategory);
    return category?.name || 'Menu Items';
  }
}
