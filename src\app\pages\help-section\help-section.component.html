<!-- Hero Section -->
<section class="hero-section py-5">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-8 mx-auto text-center">
        <h1 class="display-4 fw-bold text-white mb-4">How Can We Help You?</h1>
        <p class="lead text-white mb-4">
          Find answers to common questions, get cooking tips, and learn how to make the most of FoodieHub.
        </p>

        <!-- Search Bar -->
        <div class="search-container mx-auto" style="max-width: 600px;">
          <div class="input-group input-group-lg">
            <input type="text" class="form-control" placeholder="Search for help topics, recipes, or cooking tips...">
            <button class="btn btn-light" type="button">
              <i class="fas fa-search"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Quick Help Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title">Quick Help</h2>
      <p class="text-muted">Get instant access to our most popular help resources</p>
    </div>

    <div class="row">
      <div class="col-lg-3 col-md-6 mb-4" *ngFor="let item of quickHelp">
        <div class="quick-help-card text-center p-4 h-100">
          <div class="help-icon mb-3">
            <i [class]="item.icon + ' fa-3x text-primary-custom'"></i>
          </div>
          <h5 class="fw-bold">{{ item.title }}</h5>
          <p class="text-muted mb-3">{{ item.description }}</p>
          <a [href]="item.link" class="btn btn-outline-primary">
            Learn More
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-5">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title">Frequently Asked Questions</h2>
      <p class="text-muted">Find answers to the most common questions about FoodieHub</p>
    </div>

    <div class="row">
      <div class="col-lg-10 mx-auto">
        <div class="faq-container">
          <div class="faq-category mb-5" *ngFor="let category of faqCategories; let categoryIndex = index">
            <div class="category-header mb-4">
              <h4 class="fw-bold d-flex align-items-center">
                <i [class]="category.icon + ' text-primary-custom me-3'"></i>
                {{ category.title }}
              </h4>
            </div>

            <div class="accordion" [id]="'accordion-' + categoryIndex">
              <div class="accordion-item mb-3" *ngFor="let faq of category.faqs; let faqIndex = index">
                <h5 class="accordion-header">
                  <button class="accordion-button"
                          [class.collapsed]="!faq.isOpen"
                          type="button"
                          (click)="toggleFaq(categoryIndex, faqIndex)"
                          [attr.aria-expanded]="faq.isOpen">
                    {{ faq.question }}
                  </button>
                </h5>
                <div class="accordion-collapse collapse"
                     [class.show]="faq.isOpen">
                  <div class="accordion-body">
                    {{ faq.answer }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Contact Support Section -->
<section class="py-5 bg-cream">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 text-center">
        <h3 class="fw-bold mb-3">Still Need Help?</h3>
        <p class="text-muted mb-4">
          Can't find what you're looking for? Our support team is here to help you with any questions or issues.
        </p>

        <div class="row">
          <div class="col-md-4 mb-3">
            <div class="contact-option p-4">
              <i class="fas fa-envelope fa-2x text-primary-custom mb-3"></i>
              <h6 class="fw-bold">Email Support</h6>
              <p class="text-muted mb-3">Get help via email</p>
              <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                Send Email
              </a>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="contact-option p-4">
              <i class="fas fa-comments fa-2x text-primary-custom mb-3"></i>
              <h6 class="fw-bold">Live Chat</h6>
              <p class="text-muted mb-3">Chat with our team</p>
              <button class="btn btn-outline-primary">
                Start Chat
              </button>
            </div>
          </div>

          <div class="col-md-4 mb-3">
            <div class="contact-option p-4">
              <i class="fas fa-users fa-2x text-primary-custom mb-3"></i>
              <h6 class="fw-bold">Community</h6>
              <p class="text-muted mb-3">Ask the community</p>
              <a href="#forum" class="btn btn-outline-primary">
                Join Forum
              </a>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <small class="text-muted">
            <i class="fas fa-clock me-1"></i>
            Support hours: Monday - Friday, 9 AM - 6 PM EST
          </small>
        </div>
      </div>
    </div>
  </div>
</section>
