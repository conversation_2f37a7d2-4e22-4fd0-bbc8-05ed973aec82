/* Restaurant Header Styles */
.restaurant-header {
  position: relative;
  overflow: hidden;
}

.restaurant-overlay {
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
}

.restaurant-logo {
  border: 4px solid white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.restaurant-status .badge {
  font-size: 0.9rem;
  padding: 0.5rem 0.75rem;
}

/* Restaurant Details Bar */
.restaurant-details {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.detail-item {
  padding: 0.5rem;
}

.detail-item i {
  font-size: 1.5rem;
  display: block;
}

/* Category Sidebar */
.category-sidebar {
  background: white;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.list-group-item {
  border: none;
  border-radius: 10px !important;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.list-group-item:hover {
  background-color: #f8f9fa;
  transform: translateX(5px);
}

.list-group-item.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  transform: translateX(5px);
}

.list-group-item.active .badge {
  background-color: white !important;
  color: var(--primary-color) !important;
}

/* Food Item Cards */
.food-item-card {
  transition: all 0.3s ease;
  background: white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1px solid #e9ecef !important;
}

.food-item-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-color: var(--primary-color) !important;
}

.food-item-image {
  transition: transform 0.3s ease;
}

.food-item-card:hover .food-item-image {
  transform: scale(1.05);
}

.food-tags .badge {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
  max-height: 2.8em;
}

.price {
  font-size: 1.1rem;
}

.rating {
  font-size: 0.9rem;
}

/* Loading and Error States */
.loading-container,
.error-container {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Responsive Design */
@media (max-width: 992px) {
  .category-sidebar {
    position: static !important;
    margin-bottom: 2rem;
  }
  
  .restaurant-logo {
    width: 80px !important;
    height: 80px !important;
  }
  
  .restaurant-info h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .restaurant-header .restaurant-cover-image img {
    height: 250px !important;
  }
  
  .restaurant-info {
    position: static !important;
    background: rgba(0,0,0,0.8);
    margin-top: -100px;
    position: relative !important;
    z-index: 10;
  }
  
  .restaurant-logo {
    width: 60px !important;
    height: 60px !important;
  }
  
  .restaurant-info h1 {
    font-size: 1.5rem;
  }
  
  .restaurant-info .lead {
    font-size: 1rem;
  }
  
  .detail-item {
    margin-bottom: 1rem;
  }
  
  .food-item-card .row {
    flex-direction: column;
  }
  
  .food-item-card .col-4,
  .food-item-card .col-8 {
    flex: none;
    width: 100%;
    max-width: 100%;
  }
  
  .food-item-card .col-8 {
    padding-left: 0 !important;
    padding-top: 1rem;
  }
  
  .food-item-image {
    min-height: 150px !important;
  }
}

@media (max-width: 576px) {
  .restaurant-details .row {
    text-align: left !important;
  }
  
  .restaurant-details .col-6 {
    margin-bottom: 1rem;
  }
  
  .category-sidebar {
    padding: 1rem;
  }
  
  .list-group-item {
    font-size: 0.9rem;
    padding: 0.75rem;
  }
  
  .food-item-card {
    margin-bottom: 1rem;
  }
}

/* Animation for smooth transitions */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.food-item-card {
  animation: slideInRight 0.3s ease-out;
}

/* Custom scrollbar for category sidebar */
.category-sidebar::-webkit-scrollbar {
  width: 6px;
}

.category-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.category-sidebar::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

.category-sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}
