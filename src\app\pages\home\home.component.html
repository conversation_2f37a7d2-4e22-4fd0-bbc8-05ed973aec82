<!-- Hero Section -->
<section class="hero-section">
  <div class="container">
    <div class="row align-items-center min-vh-75">
      <div class="col-lg-8 mx-auto text-center">
        <h1 class="display-4 fw-bold mb-4 text-white">
          Discover Amazing
          <span class="text-warning">Restaurants</span> &
          <span class="text-warning">Order Delicious Food</span>
        </h1>
        <p class="lead mb-4 text-white">
          Find the best restaurants in your area and order your favorite dishes
          with fast delivery right to your doorstep.
        </p>

        <!-- Restaurant Search -->
        <div class="search-container mx-auto mb-4" style="max-width: 600px;">
          <div class="position-relative">
            <div class="input-group input-group-lg">
              <span class="input-group-text bg-white border-end-0">
                <i class="fas fa-search text-muted"></i>
              </span>
              <input
                type="text"
                class="form-control border-start-0 ps-0"
                placeholder="Search for restaurants, cuisines, or dishes..."
                [(ngModel)]="searchQuery"
                (input)="onSearchInput()"
                (focus)="onSearchInput()">
              <button
                *ngIf="searchQuery"
                class="btn btn-outline-secondary"
                type="button"
                (click)="clearSearch()">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <!-- Search Results Dropdown -->
            <div *ngIf="showSearchResults" class="search-results position-absolute w-100 bg-white rounded-3 shadow-lg mt-2 p-3" style="z-index: 1000; max-height: 400px; overflow-y: auto;">
              <div *ngIf="isSearching" class="text-center py-3">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                  <span class="visually-hidden">Searching...</span>
                </div>
                <span class="ms-2 text-muted">Searching restaurants...</span>
              </div>

              <div *ngIf="!isSearching && filteredRestaurants.length === 0" class="text-center py-3 text-muted">
                <i class="fas fa-search fa-2x mb-2"></i>
                <p class="mb-0">No restaurants found for "{{ searchQuery }}"</p>
              </div>

              <div *ngIf="!isSearching && filteredRestaurants.length > 0">
                <h6 class="text-muted mb-3">Found {{ filteredRestaurants.length }} restaurant(s)</h6>
                <div class="restaurant-result mb-2"
                     *ngFor="let restaurant of filteredRestaurants.slice(0, 5)"
                     (click)="selectRestaurant(restaurant)">
                  <div class="d-flex align-items-center p-2 rounded hover-bg-light cursor-pointer">
                    <img [src]="restaurant.images.logo"
                         [alt]="restaurant.name"
                         class="rounded me-3"
                         style="width: 50px; height: 50px; object-fit: cover;">
                    <div class="flex-grow-1">
                      <h6 class="mb-1 fw-bold">{{ restaurant.name }}</h6>
                      <p class="mb-1 text-muted small">{{ restaurant.cuisine.join(', ') }}</p>
                      <div class="d-flex align-items-center">
                        <span class="badge bg-warning text-dark me-2">
                          <i class="fas fa-star"></i> {{ restaurant.rating }}
                        </span>
                        <span class="text-muted small">{{ restaurant.deliveryTime }}</span>
                        <span class="text-muted small ms-2">{{ getPriceRangeDisplay(restaurant.priceRange) }}</span>
                      </div>
                    </div>
                    <i class="fas fa-chevron-right text-muted"></i>
                  </div>
                </div>

                <div *ngIf="filteredRestaurants.length > 5" class="text-center mt-3">
                  <button class="btn btn-outline-primary btn-sm">
                    View all {{ filteredRestaurants.length }} results
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="d-flex flex-wrap justify-content-center gap-3 mb-4">
          <button class="btn btn-light btn-lg px-4">
            <i class="fas fa-map-marker-alt me-2"></i>
            Find Nearby
          </button>
          <button class="btn btn-outline-light btn-lg px-4">
            <i class="fas fa-filter me-2"></i>
            Filter Options
          </button>
        </div>

        <div class="text-white">
          <small class="opacity-75">
            <i class="fas fa-store me-2"></i>
            Over 500+ restaurants available
          </small>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Featured Restaurants Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title">Featured Restaurants</h2>
      <p class="text-muted">Top-rated restaurants loved by our community</p>
    </div>

    <div class="row">
      <div class="col-lg-4 col-md-6 mb-4" *ngFor="let restaurant of featuredRestaurants">
        <div class="card restaurant-card h-100" (click)="selectRestaurant(restaurant)">
          <div class="position-relative">
            <img [src]="restaurant.images.cover"
                 [alt]="restaurant.name"
                 class="card-img-top"
                 style="height: 250px; object-fit: cover;">
            <div class="position-absolute top-0 start-0 m-3">
              <span class="badge bg-success" *ngIf="restaurant.isOpen">
                <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                Open
              </span>
              <span class="badge bg-danger" *ngIf="!restaurant.isOpen">
                <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                Closed
              </span>
            </div>
            <div class="position-absolute top-0 end-0 m-3">
              <span class="badge bg-warning text-dark">
                <i class="fas fa-star me-1"></i>
                {{ restaurant.rating }}
              </span>
            </div>
            <div class="position-absolute bottom-0 start-0 end-0 restaurant-overlay p-3">
              <div class="d-flex justify-content-between align-items-end">
                <div>
                  <span class="badge bg-primary me-1" *ngFor="let cuisine of restaurant.cuisine.slice(0, 2)">
                    {{ cuisine }}
                  </span>
                </div>
                <div class="text-white">
                  <small>{{ getPriceRangeDisplay(restaurant.priceRange) }}</small>
                </div>
              </div>
            </div>
          </div>

          <div class="card-body d-flex flex-column">
            <div class="d-flex align-items-start justify-content-between mb-2">
              <h5 class="card-title mb-0">{{ restaurant.name }}</h5>
              <img [src]="restaurant.images.logo"
                   [alt]="restaurant.name + ' logo'"
                   class="restaurant-logo rounded"
                   style="width: 40px; height: 40px; object-fit: cover;">
            </div>

            <p class="card-text text-muted flex-grow-1">{{ restaurant.description }}</p>

            <div class="restaurant-info mt-auto">
              <div class="d-flex justify-content-between align-items-center mb-2">
                <div class="d-flex align-items-center">
                  <i class="fas fa-clock text-primary-custom me-1"></i>
                  <small class="text-muted">{{ restaurant.deliveryTime }}</small>
                </div>
                <div class="d-flex align-items-center">
                  <i class="fas fa-motorcycle text-primary-custom me-1"></i>
                  <small class="text-muted">${{ restaurant.deliveryFee }}</small>
                </div>
              </div>

              <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                  <span class="me-2" *ngFor="let star of getStarRating(restaurant.rating)">
                    <i [class]="star" style="color: #ffc107; font-size: 0.8rem;"></i>
                  </span>
                  <small class="text-muted">({{ restaurant.reviewCount }})</small>
                </div>
                <small class="text-muted">Min: ${{ restaurant.minimumOrder }}</small>
              </div>
            </div>

            <button class="btn btn-primary mt-3 w-100">
              <i class="fas fa-utensils me-2"></i>
              View Menu
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-4">
      <button class="btn btn-outline-primary btn-lg">
        <i class="fas fa-store me-2"></i>
        View All Restaurants
      </button>
    </div>
  </div>
</section>

<!-- Popular Categories Section -->
<section class="py-5">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title">Popular Categories</h2>
      <p class="text-muted">What are you craving today?</p>
    </div>

    <div class="row">
      <div class="col-lg-3 col-md-6 mb-4" *ngFor="let category of popularCategories">
        <div class="category-card h-100 position-relative overflow-hidden rounded-3">
          <img [src]="category.image"
               [alt]="category.name"
               class="category-image w-100 h-100"
               style="height: 200px; object-fit: cover;">
          <div class="category-overlay position-absolute top-0 start-0 w-100 h-100 d-flex flex-column justify-content-end p-4">
            <div class="text-white">
              <div class="category-icon mb-2" style="font-size: 2.5rem;">{{ category.icon }}</div>
              <h5 class="fw-bold mb-1">{{ category.name }}</h5>
              <small class="opacity-75">{{ category.count }}</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Cuisine Types Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title">Explore by Cuisine</h2>
      <p class="text-muted">Discover flavors from around the world</p>
    </div>

    <div class="row">
      <div class="col-lg-2 col-md-4 col-6 mb-4" *ngFor="let cuisine of cuisineTypes">
        <div class="text-center cuisine-card p-4 h-100 border rounded-3" (click)="searchByCuisine(cuisine.name)">
          <div class="cuisine-icon mb-3" style="font-size: 3rem;">{{ cuisine.icon }}</div>
          <h6 class="fw-bold">{{ cuisine.name }}</h6>
          <small class="text-muted">{{ cuisine.count }}</small>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Newsletter Section -->
<section class="py-5 bg-cream">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 text-center">
        <h3 class="fw-bold mb-3">Stay Updated with Latest Recipes</h3>
        <p class="text-muted mb-4">
          Get weekly recipe recommendations, cooking tips, and exclusive content
          delivered straight to your inbox.
        </p>

        <form class="row g-3 justify-content-center">
          <div class="col-md-6">
            <input type="email" class="form-control form-control-lg"
                   placeholder="Enter your email address" required>
          </div>
          <div class="col-md-3">
            <button type="submit" class="btn btn-primary btn-lg w-100">
              <i class="fas fa-paper-plane me-2"></i>
              Subscribe
            </button>
          </div>
        </form>

        <small class="text-muted mt-3 d-block">
          <i class="fas fa-shield-alt me-1"></i>
          We respect your privacy. Unsubscribe anytime.
        </small>
      </div>
    </div>
  </div>
</section>
