<!-- Hero Section -->
<section class="hero-section">
  <div class="container">
    <div class="row align-items-center min-vh-75">
      <div class="col-lg-6">
        <h1 class="display-4 fw-bold mb-4">
          Discover Amazing
          <span class="text-warning">Recipes</span> &
          <span class="text-warning">Culinary Adventures</span>
        </h1>
        <p class="lead mb-4">
          Join our community of food lovers and explore thousands of delicious recipes,
          cooking tips, and culinary secrets from around the world.
        </p>
        <div class="d-flex flex-wrap gap-3">
          <button class="btn btn-light btn-lg px-4">
            <i class="fas fa-search me-2"></i>
            Explore Recipes
          </button>
          <button class="btn btn-outline-light btn-lg px-4">
            <i class="fas fa-play me-2"></i>
            Watch Cooking Videos
          </button>
        </div>
        <div class="mt-4">
          <small class="text-light opacity-75">
            <i class="fas fa-users me-2"></i>
            Join 50,000+ food enthusiasts
          </small>
        </div>
      </div>
      <div class="col-lg-6 text-center">
        <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop"
             alt="Delicious food spread"
             class="img-fluid rounded-3 shadow-lg">
      </div>
    </div>
  </div>
</section>

<!-- Featured Recipes Section -->
<section class="py-5 bg-light">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title">Featured Recipes</h2>
      <p class="text-muted">Handpicked recipes that our community loves</p>
    </div>

    <div class="row">
      <div class="col-lg-4 col-md-6 mb-4" *ngFor="let recipe of featuredRecipes">
        <div class="card food-card h-100">
          <div class="position-relative">
            <img [src]="recipe.image"
                 [alt]="recipe.title"
                 class="card-img-top"
                 style="height: 250px; object-fit: cover;">
            <div class="position-absolute top-0 end-0 m-3">
              <span [class]="getDifficultyClass(recipe.difficulty)">
                {{ recipe.difficulty }}
              </span>
            </div>
          </div>

          <div class="card-body d-flex flex-column">
            <h5 class="card-title">{{ recipe.title }}</h5>
            <p class="card-text text-muted flex-grow-1">{{ recipe.description }}</p>

            <div class="d-flex justify-content-between align-items-center mt-auto">
              <div class="d-flex align-items-center">
                <i class="fas fa-clock text-primary-custom me-1"></i>
                <small class="text-muted">{{ recipe.cookTime }}</small>
              </div>
              <div class="d-flex align-items-center">
                <span class="me-2" *ngFor="let star of getStarRating(recipe.rating)">
                  <i [class]="star" style="color: #ffc107; font-size: 0.8rem;"></i>
                </span>
                <small class="text-muted">({{ recipe.rating }})</small>
              </div>
            </div>

            <button class="btn btn-primary mt-3">
              <i class="fas fa-eye me-2"></i>
              View Recipe
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center mt-4">
      <button class="btn btn-outline-primary btn-lg">
        <i class="fas fa-th-large me-2"></i>
        View All Recipes
      </button>
    </div>
  </div>
</section>

<!-- Cuisine Types Section -->
<section class="py-5">
  <div class="container">
    <div class="text-center mb-5">
      <h2 class="section-title">Explore by Cuisine</h2>
      <p class="text-muted">Discover flavors from around the world</p>
    </div>

    <div class="row">
      <div class="col-lg-2 col-md-4 col-6 mb-4" *ngFor="let cuisine of cuisineTypes">
        <div class="text-center cuisine-card p-4 h-100 border rounded-3">
          <div class="cuisine-icon mb-3" style="font-size: 3rem;">{{ cuisine.icon }}</div>
          <h6 class="fw-bold">{{ cuisine.name }}</h6>
          <small class="text-muted">{{ cuisine.count }}</small>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Newsletter Section -->
<section class="py-5 bg-cream">
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-lg-8 text-center">
        <h3 class="fw-bold mb-3">Stay Updated with Latest Recipes</h3>
        <p class="text-muted mb-4">
          Get weekly recipe recommendations, cooking tips, and exclusive content
          delivered straight to your inbox.
        </p>

        <form class="row g-3 justify-content-center">
          <div class="col-md-6">
            <input type="email" class="form-control form-control-lg"
                   placeholder="Enter your email address" required>
          </div>
          <div class="col-md-3">
            <button type="submit" class="btn btn-primary btn-lg w-100">
              <i class="fas fa-paper-plane me-2"></i>
              Subscribe
            </button>
          </div>
        </form>

        <small class="text-muted mt-3 d-block">
          <i class="fas fa-shield-alt me-1"></i>
          We respect your privacy. Unsubscribe anytime.
        </small>
      </div>
    </div>
  </div>
</section>
