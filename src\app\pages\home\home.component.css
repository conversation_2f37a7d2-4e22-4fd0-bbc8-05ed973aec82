.min-vh-75 {
  min-height: 75vh;
}

.hero-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.cuisine-card {
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
}

.cuisine-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  border-color: var(--primary-color) !important;
}

.cuisine-icon {
  transition: transform 0.3s ease;
}

.cuisine-card:hover .cuisine-icon {
  transform: scale(1.1);
}

.food-card {
  transition: all 0.3s ease;
}

.food-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.card-img-top {
  transition: transform 0.3s ease;
}

.food-card:hover .card-img-top {
  transform: scale(1.05);
}

.food-card .position-relative {
  overflow: hidden;
  border-radius: 15px 15px 0 0;
}

@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
  }

  .display-4 {
    font-size: 2.5rem;
  }

  .cuisine-card {
    margin-bottom: 1rem;
  }

  .d-flex.gap-3 {
    flex-direction: column;
    gap: 1rem !important;
  }

  .btn-lg {
    width: 100%;
  }
}