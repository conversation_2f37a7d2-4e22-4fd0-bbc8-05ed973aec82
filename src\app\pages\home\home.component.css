.min-vh-75 {
  min-height: 75vh;
}

.hero-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.cuisine-card {
  transition: all 0.3s ease;
  cursor: pointer;
  background: white;
}

.cuisine-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  border-color: var(--primary-color) !important;
}

.cuisine-icon {
  transition: transform 0.3s ease;
}

.cuisine-card:hover .cuisine-icon {
  transform: scale(1.1);
}

/* Search Styles */
.search-container .form-control {
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  font-size: 1.1rem;
  padding: 0.75rem 1rem;
}

.search-container .input-group-text {
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-results {
  border: 1px solid #e9ecef;
  animation: fadeInDown 0.3s ease;
}

.restaurant-result:hover .hover-bg-light {
  background-color: #f8f9fa !important;
}

.cursor-pointer {
  cursor: pointer;
}

/* Restaurant Card Styles */
.restaurant-card {
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.restaurant-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.restaurant-overlay {
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
}

.restaurant-logo {
  border: 2px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-img-top {
  transition: transform 0.3s ease;
}

.restaurant-card:hover .card-img-top {
  transform: scale(1.05);
}

.restaurant-card .position-relative {
  overflow: hidden;
  border-radius: 15px 15px 0 0;
}

/* Category Card Styles */
.category-card {
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.category-overlay {
  background: linear-gradient(transparent, rgba(0,0,0,0.6));
  transition: background 0.3s ease;
}

.category-card:hover .category-overlay {
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
}

.category-icon {
  transition: transform 0.3s ease;
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

/* Animations */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles */
@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0;
  }

  .display-4 {
    font-size: 2.5rem;
  }

  .search-container {
    margin-bottom: 2rem;
  }

  .search-results {
    max-height: 300px !important;
  }

  .cuisine-card {
    margin-bottom: 1rem;
  }

  .category-card {
    margin-bottom: 1.5rem;
  }

  .restaurant-card {
    margin-bottom: 1.5rem;
  }

  .d-flex.gap-3 {
    flex-direction: column;
    gap: 1rem !important;
  }

  .btn-lg {
    width: 100%;
  }

  .restaurant-info {
    font-size: 0.9rem;
  }

  .restaurant-logo {
    width: 35px !important;
    height: 35px !important;
  }
}

@media (max-width: 576px) {
  .search-container .input-group-lg .form-control {
    font-size: 1rem;
    padding: 0.6rem 0.8rem;
  }

  .category-card {
    height: 150px;
  }

  .category-icon {
    font-size: 2rem !important;
  }

  .restaurant-card .card-img-top {
    height: 200px !important;
  }
}