<!-- Loading State -->
<div *ngIf="isLoading" class="loading-container d-flex justify-content-center align-items-center" style="min-height: 50vh;">
  <div class="text-center">
    <div class="spinner-border text-primary mb-3" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="text-muted">Loading restaurant...</p>
  </div>
</div>

<!-- Error State -->
<div *ngIf="error && !isLoading" class="error-container d-flex justify-content-center align-items-center" style="min-height: 50vh;">
  <div class="text-center">
    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
    <h4>{{ error }}</h4>
    <button class="btn btn-primary mt-3" (click)="goBack()">
      <i class="fas fa-arrow-left me-2"></i>
      Go Back
    </button>
  </div>
</div>

<!-- Restaurant Content -->
<div *ngIf="restaurant && !isLoading && !error">
  <!-- Restaurant Header -->
  <section class="restaurant-header position-relative">
    <div class="restaurant-cover-image">
      <img [src]="restaurant.images.cover" 
           [alt]="restaurant.name" 
           class="w-100"
           style="height: 400px; object-fit: cover;">
      <div class="restaurant-overlay position-absolute top-0 start-0 w-100 h-100"></div>
    </div>
    
    <div class="restaurant-info position-absolute bottom-0 start-0 w-100 p-4">
      <div class="container">
        <div class="row align-items-end">
          <div class="col-md-8">
            <div class="d-flex align-items-end mb-3">
              <img [src]="restaurant.images.logo" 
                   [alt]="restaurant.name + ' logo'" 
                   class="restaurant-logo me-4"
                   style="width: 100px; height: 100px; object-fit: cover;">
              <div class="text-white">
                <h1 class="display-5 fw-bold mb-2">{{ restaurant.name }}</h1>
                <p class="lead mb-2">{{ restaurant.description }}</p>
                <div class="d-flex flex-wrap gap-2 mb-2">
                  <span class="badge bg-primary" *ngFor="let cuisine of restaurant.cuisine">
                    {{ cuisine }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4 text-md-end">
            <div class="restaurant-status text-white">
              <div class="d-flex align-items-center justify-content-md-end mb-2">
                <span class="badge me-2" [class]="restaurant.isOpen ? 'bg-success' : 'bg-danger'">
                  <i class="fas fa-circle me-1" style="font-size: 0.6rem;"></i>
                  {{ restaurant.isOpen ? 'Open' : 'Closed' }}
                </span>
                <small>{{ formatOpeningHours(restaurant.openingHours) }}</small>
              </div>
              <div class="d-flex align-items-center justify-content-md-end">
                <span class="badge bg-warning text-dark me-2">
                  <i class="fas fa-star me-1"></i>
                  {{ restaurant.rating }}
                </span>
                <small>({{ restaurant.reviewCount }} reviews)</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Restaurant Details Bar -->
  <section class="restaurant-details bg-light py-3 border-bottom">
    <div class="container">
      <div class="row text-center">
        <div class="col-md-3 col-6 mb-2">
          <div class="detail-item">
            <i class="fas fa-clock text-primary-custom mb-1"></i>
            <div class="fw-bold">{{ restaurant.deliveryTime }}</div>
            <small class="text-muted">Delivery Time</small>
          </div>
        </div>
        <div class="col-md-3 col-6 mb-2">
          <div class="detail-item">
            <i class="fas fa-motorcycle text-primary-custom mb-1"></i>
            <div class="fw-bold">${{ restaurant.deliveryFee }}</div>
            <small class="text-muted">Delivery Fee</small>
          </div>
        </div>
        <div class="col-md-3 col-6 mb-2">
          <div class="detail-item">
            <i class="fas fa-dollar-sign text-primary-custom mb-1"></i>
            <div class="fw-bold">${{ restaurant.minimumOrder }}</div>
            <small class="text-muted">Minimum Order</small>
          </div>
        </div>
        <div class="col-md-3 col-6 mb-2">
          <div class="detail-item">
            <i class="fas fa-tags text-primary-custom mb-1"></i>
            <div class="fw-bold">{{ getPriceRangeDisplay(restaurant.priceRange) }}</div>
            <small class="text-muted">Price Range</small>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Menu Section -->
  <section class="menu-section py-5">
    <div class="container">
      <div class="row">
        <!-- Category Sidebar -->
        <div class="col-lg-3 mb-4">
          <div class="category-sidebar sticky-top" style="top: 100px;">
            <h5 class="fw-bold mb-3">Menu Categories</h5>
            <div class="list-group">
              <button 
                class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                [class.active]="selectedCategory === 'all'"
                (click)="selectCategory('all')">
                <span>
                  <i class="fas fa-th-large me-2"></i>
                  All Items
                </span>
                <span class="badge bg-primary rounded-pill">{{ foodItems.length }}</span>
              </button>
              <button 
                *ngFor="let category of categories"
                class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                [class.active]="selectedCategory === category.slug"
                (click)="selectCategory(category.slug)">
                <span>
                  <span class="me-2">{{ category.icon }}</span>
                  {{ category.name }}
                </span>
                <span class="badge bg-primary rounded-pill">
                  {{ getCategoryItemCount(category.name) }}
                </span>
              </button>
            </div>
          </div>
        </div>

        <!-- Food Items -->
        <div class="col-lg-9">
          <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="fw-bold">
              {{ getSelectedCategoryName() }}
            </h4>
            <span class="text-muted">{{ filteredFoodItems.length }} items</span>
          </div>

          <div class="row">
            <div class="col-lg-6 mb-4" *ngFor="let item of filteredFoodItems">
              <div class="food-item-card h-100 p-3 border rounded-3">
                <div class="row g-0 h-100">
                  <div class="col-4">
                    <img [src]="item.images[0]" 
                         [alt]="item.name" 
                         class="food-item-image w-100 h-100 rounded-2"
                         style="object-fit: cover; min-height: 120px;">
                  </div>
                  <div class="col-8 ps-3 d-flex flex-column">
                    <div class="flex-grow-1">
                      <h6 class="fw-bold mb-1">{{ item.name }}</h6>
                      <p class="text-muted small mb-2 line-clamp-2">{{ item.shortDescription }}</p>
                      
                      <!-- Food Tags -->
                      <div class="food-tags mb-2">
                        <span class="badge bg-success me-1" *ngIf="item.isVegetarian">
                          <i class="fas fa-leaf"></i>
                        </span>
                        <span class="badge bg-info me-1" *ngIf="item.isVegan">Vegan</span>
                        <span class="badge bg-warning me-1" *ngIf="item.isSpicy">
                          <i class="fas fa-pepper-hot"></i>
                        </span>
                        <span class="badge bg-secondary me-1" *ngIf="item.isPopular">Popular</span>
                      </div>
                    </div>
                    
                    <div class="food-item-footer">
                      <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="price">
                          <span class="fw-bold text-primary-custom">${{ item.price }}</span>
                          <span *ngIf="item.originalPrice" class="text-muted text-decoration-line-through ms-1 small">
                            ${{ item.originalPrice }}
                          </span>
                        </div>
                        <div class="rating">
                          <span class="me-1" *ngFor="let star of getStarRating(item.rating)">
                            <i [class]="star" style="color: #ffc107; font-size: 0.7rem;"></i>
                          </span>
                          <small class="text-muted">({{ item.reviewCount }})</small>
                        </div>
                      </div>
                      
                      <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm flex-grow-1" (click)="viewFoodItem(item)">
                          <i class="fas fa-eye me-1"></i>
                          View
                        </button>
                        <button 
                          class="btn btn-primary btn-sm flex-grow-1" 
                          [disabled]="!item.isAvailable"
                          (click)="addToCart(item)">
                          <i class="fas fa-plus me-1"></i>
                          {{ item.isAvailable ? 'Add' : 'Unavailable' }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- No Items Message -->
          <div *ngIf="filteredFoodItems.length === 0" class="text-center py-5">
            <i class="fas fa-utensils fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No items found in this category</h5>
            <p class="text-muted">Try selecting a different category or check back later.</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
