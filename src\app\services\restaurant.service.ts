import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, BehaviorSubject } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Restaurant, RestaurantSearchFilters, RestaurantSEO } from '../models/restaurant.model';

@Injectable({
  providedIn: 'root'
})
export class RestaurantService {
  private apiUrl = 'https://api.yourfoodplatform.com/restaurants';
  private selectedRestaurantSubject = new BehaviorSubject<Restaurant | null>(null);
  public selectedRestaurant$ = this.selectedRestaurantSubject.asObservable();

  constructor(private http: HttpClient) { }

  // Get all restaurants with optional filters
  getRestaurants(filters?: RestaurantSearchFilters): Observable<Restaurant[]> {
    return this.http.get<Restaurant[]>(`${this.apiUrl}`, { params: filters as any }).pipe(
      catchError(() => {
        // Fallback to mock data if API is not available
        return of(this.getMockRestaurants(filters));
      })
    );
  }

  // Get restaurant by ID
  getRestaurantById(id: string): Observable<Restaurant | null> {
    return this.http.get<Restaurant>(`${this.apiUrl}/${id}`).pipe(
      catchError(() => {
        // Fallback to mock data
        const mockRestaurants = this.getMockRestaurants();
        const restaurant = mockRestaurants.find(r => r.id === id);
        return of(restaurant || null);
      })
    );
  }

  // Get restaurant by slug (SEO-friendly URL)
  getRestaurantBySlug(slug: string): Observable<Restaurant | null> {
    return this.http.get<Restaurant>(`${this.apiUrl}/slug/${slug}`).pipe(
      catchError(() => {
        // Fallback to mock data
        const mockRestaurants = this.getMockRestaurants();
        const restaurant = mockRestaurants.find(r => r.slug === slug);
        return of(restaurant || null);
      })
    );
  }

  // Search restaurants by name
  searchRestaurants(query: string): Observable<Restaurant[]> {
    return this.getRestaurants({ query }).pipe(
      map(restaurants => restaurants.filter(r =>
        r.name.toLowerCase().includes(query.toLowerCase()) ||
        r.cuisine.some(c => c.toLowerCase().includes(query.toLowerCase())) ||
        r.tags.some(t => t.toLowerCase().includes(query.toLowerCase()))
      ))
    );
  }

  // Get featured restaurants
  getFeaturedRestaurants(): Observable<Restaurant[]> {
    return this.getRestaurants({ featured: true });
  }

  // Set selected restaurant
  setSelectedRestaurant(restaurant: Restaurant | null): void {
    this.selectedRestaurantSubject.next(restaurant);
  }

  // Get restaurant SEO data
  getRestaurantSEO(restaurantSlug: string): Observable<RestaurantSEO> {
    return this.http.get<RestaurantSEO>(`${this.apiUrl}/${restaurantSlug}/seo`).pipe(
      catchError(() => {
        // Fallback to generated SEO data
        return this.getRestaurantBySlug(restaurantSlug).pipe(
          map(restaurant => this.generateRestaurantSEO(restaurant))
        );
      })
    );
  }

  // Generate SEO data for a restaurant
  private generateRestaurantSEO(restaurant: Restaurant | null): RestaurantSEO {
    if (!restaurant) {
      return this.getDefaultSEO();
    }

    const baseUrl = 'https://yourfoodplatform.com';
    const cuisineText = restaurant.cuisine.join(', ');

    return {
      title: `${restaurant.name} - ${cuisineText} Restaurant | Order Online`,
      description: `Order delicious ${cuisineText} food from ${restaurant.name}. ${restaurant.description} ⭐ ${restaurant.rating}/5 rating with ${restaurant.reviewCount} reviews. Fast delivery!`,
      keywords: `${restaurant.name}, ${cuisineText}, food delivery, restaurant, order online, ${restaurant.tags.join(', ')}`,
      ogTitle: `${restaurant.name} - Delicious ${cuisineText} Food`,
      ogDescription: `Experience amazing ${cuisineText} cuisine at ${restaurant.name}. Order now for fast delivery!`,
      ogImage: restaurant.images.cover,
      ogUrl: `${baseUrl}/restaurant/${restaurant.slug}`,
      twitterCard: 'summary_large_image',
      twitterTitle: `${restaurant.name} - ${cuisineText} Restaurant`,
      twitterDescription: `Order delicious food from ${restaurant.name}. ${restaurant.rating}⭐ rating!`,
      twitterImage: restaurant.images.cover,
      structuredData: this.generateStructuredData(restaurant)
    };
  }

  // Generate JSON-LD structured data for restaurant
  private generateStructuredData(restaurant: Restaurant): any {
    return {
      "@context": "https://schema.org",
      "@type": "Restaurant",
      "name": restaurant.name,
      "description": restaurant.description,
      "image": restaurant.images.cover,
      "url": `https://yourfoodplatform.com/restaurant/${restaurant.slug}`,
      "telephone": restaurant.contact.phone,
      "email": restaurant.contact.email,
      "address": {
        "@type": "PostalAddress",
        "streetAddress": restaurant.address.street,
        "addressLocality": restaurant.address.city,
        "addressRegion": restaurant.address.state,
        "postalCode": restaurant.address.zipCode,
        "addressCountry": restaurant.address.country
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": restaurant.rating,
        "reviewCount": restaurant.reviewCount,
        "bestRating": 5,
        "worstRating": 1
      },
      "priceRange": this.getPriceRangeSymbol(restaurant.priceRange),
      "servesCuisine": restaurant.cuisine,
      "openingHours": this.formatOpeningHours(restaurant.openingHours)
    };
  }

  private getPriceRangeSymbol(priceRange: string): string {
    switch (priceRange) {
      case 'budget': return '$';
      case 'mid-range': return '$$';
      case 'fine-dining': return '$$$';
      default: return '$$';
    }
  }

  private formatOpeningHours(openingHours: any): string[] {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayAbbr = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'];

    return days.map((day, index) => {
      const schedule = openingHours[day];
      if (schedule.isOpen && schedule.openTime && schedule.closeTime) {
        return `${dayAbbr[index]} ${schedule.openTime}-${schedule.closeTime}`;
      }
      return '';
    }).filter(Boolean);
  }

  private getDefaultSEO(): RestaurantSEO {
    return {
      title: 'FoodieHub - Discover Amazing Restaurants & Order Online',
      description: 'Discover and order from the best restaurants in your area. Fast delivery, great food, amazing experience.',
      keywords: 'restaurants, food delivery, order online, cuisine, dining',
      ogTitle: 'FoodieHub - Your Food Delivery Platform',
      ogDescription: 'Order from the best restaurants with fast delivery',
      ogImage: 'https://yourfoodplatform.com/assets/images/default-og.jpg',
      ogUrl: 'https://yourfoodplatform.com/',
      twitterCard: 'summary_large_image'
    };
  }

  // Mock data for development/fallback
  private getMockRestaurants(filters?: RestaurantSearchFilters): Restaurant[] {
    const mockRestaurants: Restaurant[] = [
      {
        id: '1',
        name: 'Bella Italia',
        slug: 'bella-italia',
        description: 'Authentic Italian cuisine with fresh ingredients and traditional recipes passed down through generations.',
        cuisine: ['Italian', 'Mediterranean'],
        rating: 4.8,
        reviewCount: 324,
        priceRange: 'mid-range',
        deliveryTime: '25-35 min',
        deliveryFee: 2.99,
        minimumOrder: 15,
        isOpen: true,
        openingHours: {
          monday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          tuesday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          wednesday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          thursday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          friday: { isOpen: true, openTime: '11:00', closeTime: '23:00' },
          saturday: { isOpen: true, openTime: '11:00', closeTime: '23:00' },
          sunday: { isOpen: true, openTime: '12:00', closeTime: '21:00' }
        },
        contact: {
          phone: '******-0123',
          email: '<EMAIL>',
          website: 'https://bellaitalia.com'
        },
        address: {
          street: '123 Main Street',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        images: {
          logo: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=200&h=200&fit=crop',
          cover: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800&h=400&fit=crop',
          gallery: [
            'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400&h=300&fit=crop'
          ]
        },
        tags: ['pasta', 'pizza', 'wine', 'romantic', 'family-friendly'],
        featured: true,
        verified: true,
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '2',
        name: 'grill house',
        slug: 'grill-house',
        description: 'Authentic Italian cuisine with fresh ingredients and traditional recipes passed down through generations.',
        cuisine: ['Italian', 'Mediterranean'],
        rating: 4.8,
        reviewCount: 324,
        priceRange: 'mid-range',
        deliveryTime: '25-35 min',
        deliveryFee: 2.99,
        minimumOrder: 15,
        isOpen: true,
        openingHours: {
          monday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          tuesday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          wednesday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          thursday: { isOpen: true, openTime: '11:00', closeTime: '22:00' },
          friday: { isOpen: true, openTime: '11:00', closeTime: '23:00' },
          saturday: { isOpen: true, openTime: '11:00', closeTime: '23:00' },
          sunday: { isOpen: true, openTime: '12:00', closeTime: '21:00' }
        },
        contact: {
          phone: '******-0123',
          email: '<EMAIL>',
          website: 'https://bellaitalia.com'
        },
        address: {
          street: '128 Main Street',
          city: 'New York',
          state: 'NY',
          zipCode: '10001',
          country: 'USA'
        },
        images: {
          logo: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=200&h=200&fit=crop',
          cover: 'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=800&h=400&fit=crop',
          gallery: [
            'https://images.unsplash.com/photo-1555396273-367ea4eb4db5?w=400&h=300&fit=crop',
            'https://images.unsplash.com/photo-1574071318508-1cdbab80d002?w=400&h=300&fit=crop'
          ]
        },
        tags: ['pasta', 'pizza', 'wine', 'romantic', 'family-friendly'],
        featured: true,
        verified: true,
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-01-15')
      }
    ];

    // Apply filters if provided
    if (filters) {
      return this.applyFilters(mockRestaurants, filters);
    }

    return mockRestaurants;
  }

  private applyFilters(restaurants: Restaurant[], filters: RestaurantSearchFilters): Restaurant[] {
    return restaurants.filter(restaurant => {
      if (filters.query && !restaurant.name.toLowerCase().includes(filters.query.toLowerCase())) {
        return false;
      }
      if (filters.featured !== undefined && restaurant.featured !== filters.featured) {
        return false;
      }
      if (filters.isOpen !== undefined && restaurant.isOpen !== filters.isOpen) {
        return false;
      }
      // Add more filter logic as needed
      return true;
    });
  }
}
