.text-light-gray {
  color: #adb5bd !important;
}

.social-links a {
  font-size: 1.2rem;
  transition: color 0.3s ease, transform 0.3s ease;
}

.social-links a:hover {
  color: var(--primary-color) !important;
  transform: translateY(-2px);
}

.newsletter-form .form-control {
  border: 1px solid #495057;
  background-color: #343a40;
  color: #fff;
}

.newsletter-form .form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(255, 107, 53, 0.25);
  background-color: #343a40;
  color: #fff;
}

.newsletter-form .form-control::placeholder {
  color: #adb5bd;
}

footer a:hover {
  color: var(--primary-color) !important;
  transition: color 0.3s ease;
}

.border-secondary {
  border-color: #495057 !important;
}

@media (max-width: 768px) {
  footer .col-md-6.text-md-end {
    text-align: center !important;
    margin-top: 1rem;
  }

  footer .social-links {
    text-align: center;
    margin-top: 1rem;
  }
}