import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { FoodItem, FoodCategory, FoodItemSearchFilters } from '../models/food-item.model';

@Injectable({
  providedIn: 'root'
})
export class FoodItemService {
  private apiUrl = 'https://api.yourfoodplatform.com/food-items';

  constructor(private http: HttpClient) { }

  // Get food items by restaurant ID
  getFoodItemsByRestaurant(restaurantId: string, filters?: FoodItemSearchFilters): Observable<FoodItem[]> {
    const url = `${this.apiUrl}/restaurant/${restaurantId}`;
    return this.http.get<FoodItem[]>(url, { params: filters as any }).pipe(
      catchError(() => {
        // Fallback to mock data
        return of(this.getMockFoodItems(restaurantId, filters));
      })
    );
  }

  // Get food item by ID
  getFoodItemById(id: string): Observable<FoodItem | null> {
    return this.http.get<FoodItem>(`${this.apiUrl}/${id}`).pipe(
      catchError(() => {
        // Fallback to mock data
        const mockItems = this.getMockFoodItems();
        const item = mockItems.find(item => item.id === id);
        return of(item || null);
      })
    );
  }

  // Get food item by slug
  getFoodItemBySlug(restaurantSlug: string, itemSlug: string): Observable<FoodItem | null> {
    return this.http.get<FoodItem>(`${this.apiUrl}/restaurant/${restaurantSlug}/item/${itemSlug}`).pipe(
      catchError(() => {
        // Fallback to mock data
        const mockItems = this.getMockFoodItems();
        const item = mockItems.find(item => item.slug === itemSlug);
        return of(item || null);
      })
    );
  }

  // Get popular food items for a restaurant
  getPopularItems(restaurantId: string): Observable<FoodItem[]> {
    return this.getFoodItemsByRestaurant(restaurantId, { isPopular: true });
  }

  // Get featured food items for a restaurant
  getFeaturedItems(restaurantId: string): Observable<FoodItem[]> {
    return this.getFoodItemsByRestaurant(restaurantId).pipe(
      map(items => items.filter(item => item.isFeatured))
    );
  }

  // Get food categories for a restaurant
  getFoodCategories(restaurantId: string): Observable<FoodCategory[]> {
    return this.http.get<FoodCategory[]>(`${this.apiUrl}/restaurant/${restaurantId}/categories`).pipe(
      catchError(() => {
        // Fallback to mock categories
        return of(this.getMockCategories());
      })
    );
  }

  // Search food items
  searchFoodItems(restaurantId: string, query: string): Observable<FoodItem[]> {
    return this.getFoodItemsByRestaurant(restaurantId, { query });
  }

  // Get items by category
  getItemsByCategory(restaurantId: string, category: string): Observable<FoodItem[]> {
    return this.getFoodItemsByRestaurant(restaurantId, { category: [category] });
  }

  // Mock data for development/fallback
  private getMockFoodItems(restaurantId?: string, filters?: FoodItemSearchFilters): FoodItem[] {
    const mockItems: FoodItem[] = [
      {
        id: '1',
        restaurantId: '1',
        name: 'Margherita Pizza',
        slug: 'margherita-pizza',
        description: 'Classic Italian pizza with fresh mozzarella, tomato sauce, and basil leaves on a crispy thin crust.',
        shortDescription: 'Classic pizza with mozzarella, tomato, and basil',
        price: 16.99,
        category: 'Pizza',
        images: [
          'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=400&h=300&fit=crop',
          'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop'
        ],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        isSpicy: false,
        allergens: ['gluten', 'dairy'],
        ingredients: ['pizza dough', 'tomato sauce', 'mozzarella cheese', 'fresh basil', 'olive oil'],
        preparationTime: 15,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.7,
        reviewCount: 89,
        tags: ['classic', 'vegetarian', 'italian'],
        nutritionalInfo: {
          calories: 285,
          protein: 12,
          carbohydrates: 36,
          fat: 10,
          fiber: 2,
          sugar: 4,
          sodium: 640,
          servingSize: '1 slice (1/8 pizza)'
        },
        customizations: [
          {
            id: 'crust',
            name: 'Crust Type',
            type: 'single',
            required: true,
            options: [
              { id: 'thin', name: 'Thin Crust', price: 0, isDefault: true },
              { id: 'thick', name: 'Thick Crust', price: 2 },
              { id: 'gluten-free', name: 'Gluten-Free Crust', price: 3 }
            ]
          },
          {
            id: 'size',
            name: 'Size',
            type: 'single',
            required: true,
            options: [
              { id: 'small', name: 'Small (10")', price: 0 },
              { id: 'medium', name: 'Medium (12")', price: 3, isDefault: true },
              { id: 'large', name: 'Large (14")', price: 6 }
            ]
          }
        ],
        addOns: [
          {
            id: 'extra-cheese',
            name: 'Extra Cheese',
            description: 'Additional mozzarella cheese',
            price: 2.50,
            isAvailable: true
          },
          {
            id: 'pepperoni',
            name: 'Pepperoni',
            description: 'Spicy pepperoni slices',
            price: 3.00,
            isAvailable: true
          }
        ],
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '2',
        restaurantId: '1',
        name: 'Spaghetti Carbonara',
        slug: 'spaghetti-carbonara',
        description: 'Traditional Roman pasta dish with eggs, cheese, pancetta, and black pepper. Creamy and indulgent.',
        shortDescription: 'Creamy pasta with pancetta and cheese',
        price: 18.99,
        category: 'Pasta',
        images: [
          'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=400&h=300&fit=crop'
        ],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        isSpicy: false,
        allergens: ['gluten', 'dairy', 'eggs'],
        ingredients: ['spaghetti', 'pancetta', 'eggs', 'pecorino romano', 'black pepper', 'olive oil'],
        preparationTime: 20,
        isAvailable: true,
        isPopular: true,
        isFeatured: false,
        rating: 4.9,
        reviewCount: 156,
        tags: ['traditional', 'creamy', 'italian', 'comfort-food'],
        nutritionalInfo: {
          calories: 520,
          protein: 22,
          carbohydrates: 45,
          fat: 28,
          fiber: 3,
          sugar: 2,
          sodium: 890,
          servingSize: '1 portion'
        },
        customizations: [
          {
            id: 'pasta-type',
            name: 'Pasta Type',
            type: 'single',
            required: false,
            options: [
              { id: 'spaghetti', name: 'Spaghetti', price: 0, isDefault: true },
              { id: 'linguine', name: 'Linguine', price: 0 },
              { id: 'gluten-free', name: 'Gluten-Free Pasta', price: 2 }
            ]
          }
        ],
        addOns: [
          {
            id: 'extra-pancetta',
            name: 'Extra Pancetta',
            description: 'Additional crispy pancetta',
            price: 4.00,
            isAvailable: true
          },
          {
            id: 'truffle-oil',
            name: 'Truffle Oil',
            description: 'Drizzle of aromatic truffle oil',
            price: 3.50,
            isAvailable: true
          }
        ],
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '3',
        restaurantId: '1',
        name: 'Tiramisu',
        slug: 'tiramisu',
        description: 'Classic Italian dessert with layers of coffee-soaked ladyfingers and mascarpone cream, dusted with cocoa.',
        shortDescription: 'Classic coffee-flavored Italian dessert',
        price: 8.99,
        category: 'Desserts',
        images: [
          'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400&h=300&fit=crop'
        ],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        isSpicy: false,
        allergens: ['gluten', 'dairy', 'eggs'],
        ingredients: ['ladyfingers', 'mascarpone', 'eggs', 'sugar', 'coffee', 'cocoa powder', 'marsala wine'],
        preparationTime: 5,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.8,
        reviewCount: 203,
        tags: ['dessert', 'coffee', 'italian', 'classic'],
        nutritionalInfo: {
          calories: 240,
          protein: 6,
          carbohydrates: 28,
          fat: 12,
          fiber: 1,
          sugar: 20,
          sodium: 85,
          servingSize: '1 slice'
        },
        customizations: [],
        addOns: [
          {
            id: 'extra-cocoa',
            name: 'Extra Cocoa Dusting',
            description: 'Additional cocoa powder on top',
            price: 0.50,
            isAvailable: true
          }
        ],
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: '4',
        restaurantId: '2',
        name: 'Tiramisu',
        slug: 'tiramisu',
        description: 'Classic Italian dessert with layers of coffee-soaked ladyfingers and mascarpone cream, dusted with cocoa.',
        shortDescription: 'Classic coffee-flavored Italian dessert',
        price: 8.99,
        category: 'Desserts',
        images: [
          'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=400&h=300&fit=crop'
        ],
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        isSpicy: false,
        allergens: ['gluten', 'dairy', 'eggs'],
        ingredients: ['ladyfingers', 'mascarpone', 'eggs', 'sugar', 'coffee', 'cocoa powder', 'marsala wine'],
        preparationTime: 5,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.8,
        reviewCount: 203,
        tags: ['dessert', 'coffee', 'italian', 'classic'],
        nutritionalInfo: {
          calories: 240,
          protein: 6,
          carbohydrates: 28,
          fat: 12,
          fiber: 1,
          sugar: 20,
          sodium: 85,
          servingSize: '1 slice'
        },
        customizations: [],
        addOns: [
          {
            id: 'extra-cocoa',
            name: 'Extra Cocoa Dusting',
            description: 'Additional cocoa powder on top',
            price: 0.50,
            isAvailable: true
          }
        ],
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-01-15')
      },
      // Spice Garden (Indian) items
      {
        id: '4',
        restaurantId: '2',
        name: 'Chicken Biryani',
        slug: 'chicken-biryani',
        description: 'Aromatic basmati rice cooked with tender chicken, exotic spices, and saffron. Served with raita and pickle.',
        shortDescription: 'Fragrant rice dish with spiced chicken',
        price: 16.99,
        category: 'Main Course',
        images: [
          'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400&h=300&fit=crop'
        ],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isSpicy: true,
        spiceLevel: 3,
        allergens: ['dairy'],
        ingredients: ['basmati rice', 'chicken', 'yogurt', 'onions', 'spices', 'saffron'],
        preparationTime: 25,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.8,
        reviewCount: 156,
        tags: ['biryani', 'spicy', 'traditional', 'rice'],
        customizations: [],
        addOns: [],
        createdAt: new Date('2023-02-10'),
        updatedAt: new Date('2024-01-15')
      },
      // Burger Palace items
      {
        id: '5',
        restaurantId: '3',
        name: 'Classic Cheeseburger',
        slug: 'classic-cheeseburger',
        description: 'Juicy beef patty with melted cheese, lettuce, tomato, onion, and special sauce on a toasted bun.',
        shortDescription: 'Classic beef burger with cheese',
        price: 12.99,
        category: 'Burgers',
        images: [
          'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=400&h=300&fit=crop'
        ],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        isSpicy: false,
        allergens: ['gluten', 'dairy'],
        ingredients: ['beef patty', 'cheese', 'lettuce', 'tomato', 'onion', 'bun'],
        preparationTime: 12,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.6,
        reviewCount: 234,
        tags: ['burger', 'cheese', 'classic', 'beef'],
        customizations: [],
        addOns: [],
        createdAt: new Date('2023-03-05'),
        updatedAt: new Date('2024-01-15')
      },
      // Sushi Zen items
      {
        id: '6',
        restaurantId: '4',
        name: 'Salmon Sashimi',
        slug: 'salmon-sashimi',
        description: 'Fresh Atlantic salmon sliced to perfection, served with wasabi, pickled ginger, and soy sauce.',
        shortDescription: 'Fresh salmon sashimi',
        price: 18.99,
        category: 'Sashimi',
        images: [
          'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?w=400&h=300&fit=crop'
        ],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isSpicy: false,
        allergens: ['fish'],
        ingredients: ['fresh salmon', 'wasabi', 'pickled ginger', 'soy sauce'],
        preparationTime: 8,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.9,
        reviewCount: 89,
        tags: ['sashimi', 'fresh', 'salmon', 'japanese'],
        customizations: [],
        addOns: [],
        createdAt: new Date('2023-04-12'),
        updatedAt: new Date('2024-01-15')
      },
      // Pizza Corner items
      {
        id: '7',
        restaurantId: '5',
        name: 'Pepperoni Pizza',
        slug: 'pepperoni-pizza',
        description: 'Classic New York style thin crust pizza with pepperoni, mozzarella cheese, and tomato sauce.',
        shortDescription: 'Classic pepperoni pizza',
        price: 14.99,
        category: 'Pizza',
        images: [
          'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop'
        ],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        isSpicy: false,
        allergens: ['gluten', 'dairy'],
        ingredients: ['pizza dough', 'tomato sauce', 'mozzarella', 'pepperoni'],
        preparationTime: 15,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.4,
        reviewCount: 312,
        tags: ['pizza', 'pepperoni', 'classic', 'ny-style'],
        customizations: [],
        addOns: [],
        createdAt: new Date('2023-05-20'),
        updatedAt: new Date('2024-01-15')
      },
      // Taco Fiesta items
      {
        id: '8',
        restaurantId: '6',
        name: 'Carnitas Tacos',
        slug: 'carnitas-tacos',
        description: 'Three soft corn tortillas filled with slow-cooked pork carnitas, onions, cilantro, and salsa verde.',
        shortDescription: 'Authentic pork carnitas tacos',
        price: 11.99,
        category: 'Tacos',
        images: [
          'https://images.unsplash.com/photo-1565299585323-38174c4a6c7b?w=400&h=300&fit=crop'
        ],
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: true,
        isSpicy: true,
        spiceLevel: 2,
        allergens: [],
        ingredients: ['corn tortillas', 'pork carnitas', 'onions', 'cilantro', 'salsa verde'],
        preparationTime: 10,
        isAvailable: true,
        isPopular: true,
        isFeatured: true,
        rating: 4.7,
        reviewCount: 198,
        tags: ['tacos', 'carnitas', 'mexican', 'authentic'],
        customizations: [],
        addOns: [],
        createdAt: new Date('2023-06-15'),
        updatedAt: new Date('2024-01-15')
      }
    ];

    // Filter by restaurant ID if provided
    let filteredItems = restaurantId ?
      mockItems.filter(item => item.restaurantId === restaurantId) :
      mockItems;

    // Apply additional filters if provided
    if (filters) {
      filteredItems = this.applyFilters(filteredItems, filters);
    }

    return filteredItems;
  }

  private getMockCategories(): FoodCategory[] {
    return [
      {
        id: '1',
        name: 'Pizza',
        slug: 'pizza',
        description: 'Delicious wood-fired pizzas with fresh toppings',
        image: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=300&h=200&fit=crop',
        icon: '🍕',
        sortOrder: 1,
        isActive: true
      },
      {
        id: '2',
        name: 'Pasta',
        slug: 'pasta',
        description: 'Traditional Italian pasta dishes',
        image: 'https://images.unsplash.com/photo-1621996346565-e3dbc353d2e5?w=300&h=200&fit=crop',
        icon: '🍝',
        sortOrder: 2,
        isActive: true
      },
      {
        id: '3',
        name: 'Desserts',
        slug: 'desserts',
        description: 'Sweet treats to end your meal perfectly',
        image: 'https://images.unsplash.com/photo-1571877227200-a0d98ea607e9?w=300&h=200&fit=crop',
        icon: '🍰',
        sortOrder: 3,
        isActive: true
      }
    ];
  }

  private applyFilters(items: FoodItem[], filters: FoodItemSearchFilters): FoodItem[] {
    return items.filter(item => {
      if (filters.query && !item.name.toLowerCase().includes(filters.query.toLowerCase())) {
        return false;
      }
      if (filters.category && !filters.category.includes(item.category)) {
        return false;
      }
      if (filters.isPopular !== undefined && item.isPopular !== filters.isPopular) {
        return false;
      }
      if (filters.isAvailable !== undefined && item.isAvailable !== filters.isAvailable) {
        return false;
      }
      if (filters.dietary?.vegetarian && !item.isVegetarian) {
        return false;
      }
      if (filters.dietary?.vegan && !item.isVegan) {
        return false;
      }
      if (filters.dietary?.glutenFree && !item.isGlutenFree) {
        return false;
      }
      if (filters.priceRange) {
        if (item.price < filters.priceRange.min || item.price > filters.priceRange.max) {
          return false;
        }
      }
      if (filters.rating && item.rating < filters.rating) {
        return false;
      }
      return true;
    });
  }
}
