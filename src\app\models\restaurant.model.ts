export interface Restaurant {
  id: string;
  name: string;
  slug: string; // URL-friendly name for SEO
  description: string;
  cuisine: string[];
  rating: number;
  reviewCount: number;
  priceRange: 'budget' | 'mid-range' | 'fine-dining';
  deliveryTime: string;
  deliveryFee: number;
  minimumOrder: number;
  isOpen: boolean;
  openingHours: OpeningHours;
  contact: ContactInfo;
  address: Address;
  images: RestaurantImages;
  tags: string[];
  featured: boolean;
  verified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface OpeningHours {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

export interface DaySchedule {
  isOpen: boolean;
  openTime?: string; // Format: "09:00"
  closeTime?: string; // Format: "22:00"
}

export interface ContactInfo {
  phone: string;
  email: string;
  website?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface RestaurantImages {
  logo: string;
  cover: string;
  gallery: string[];
}

export interface RestaurantSearchFilters {
  query?: string;
  cuisine?: string[];
  priceRange?: string[];
  rating?: number;
  deliveryTime?: number;
  isOpen?: boolean;
  featured?: boolean;
}

export interface RestaurantSEO {
  title: string;
  description: string;
  keywords: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterCard?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  structuredData?: any; // JSON-LD structured data
}
