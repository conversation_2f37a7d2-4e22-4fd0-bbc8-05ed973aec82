.hero-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.stat-icon {
  transition: transform 0.3s ease;
}

.stat-icon:hover {
  transform: scale(1.1);
}

.story-card {
  background: white;
  border-radius: 15px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.story-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  border-color: var(--primary-color);
}

.story-icon {
  transition: transform 0.3s ease;
}

.story-card:hover .story-icon {
  transform: scale(1.1);
}

.team-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.08);
  transition: all 0.3s ease;
  border: 1px solid #f8f9fa;
}

.team-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.team-image img {
  width: 150px;
  height: 150px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.team-card:hover .team-image img {
  transform: scale(1.05);
}

.specialties .badge {
  font-size: 0.75rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #dee2e6;
}

.mission-points .fas {
  font-size: 1.2rem;
}

@media (max-width: 768px) {
  .hero-section {
    text-align: center;
  }

  .story-card {
    margin-bottom: 1.5rem;
  }

  .team-card {
    margin-bottom: 2rem;
  }

  .mission-points {
    margin-top: 2rem;
  }
}